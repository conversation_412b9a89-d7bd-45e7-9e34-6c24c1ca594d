import { beforeEach, describe, expect, it, vi } from 'vitest'
import { bookingAPIs } from '../booking.apis'
import { useBookingSlotsStore } from './booking-slots.store'

// Mock the booking APIs
vi.mock('../booking.apis', () => ({
  bookingAPIs: {
    getBookedSlots: vi.fn(),
  },
}))

describe('useBookingSlotsStore', () => {
  beforeEach(() => {
    // Reset the store before each test
    useBookingSlotsStore.getState().clearBookedSlots()
    vi.clearAllMocks()
  })

  it('should set booking page ID and load slots if date is selected', async () => {
    const mockResponse = {
      status: { success: true, message: 'Success', code: '200' },
      data: {
        bookingPageId: 'booking-page-1',
        date: '2024-01-15',
        bookedSlots: [
          {
            field: 'field-1',
            time: '10:00',
            date: '2024-01-15',
            bookingId: 'booking-1',
            status: 'confirmed' as const,
          },
        ],
      },
    }

    vi.mocked(bookingAPIs.getBookedSlots).mockResolvedValue(mockResponse)

    const store = useBookingSlotsStore.getState()

    // Set a selected date first
    store.setSelectedDate('2024-01-15')

    // Then set booking page ID - this should trigger loading slots
    await store.setBookingPageId('booking-page-1')

    expect(bookingAPIs.getBookedSlots).toHaveBeenCalledWith({
      bookingPageId: 'booking-page-1',
      date: '2024-01-15',
    })

    const state = useBookingSlotsStore.getState()

    expect(state.bookingPageId).toBe('booking-page-1')
    expect(state.bookedSlots).toHaveLength(1)
    expect(state.bookedSlots[0]?.field).toBe('field-1')
  })

  it('should set selected date and load slots if booking page ID is set', async () => {
    const mockResponse = {
      status: { success: true, message: 'Success', code: '200' },
      data: {
        bookingPageId: 'booking-page-2',
        date: '2024-01-16',
        bookedSlots: [
          {
            field: 'field-2',
            time: '14:00',
            date: '2024-01-16',
            bookingId: 'booking-2',
            status: 'pending' as const,
          },
        ],
      },
    }

    vi.mocked(bookingAPIs.getBookedSlots).mockResolvedValue(mockResponse)

    const store = useBookingSlotsStore.getState()

    // Set booking page ID first
    await store.setBookingPageId('booking-page-2')

    // Then set selected date - this should trigger loading slots
    await store.setSelectedDateAndLoadSlots('2024-01-16')

    expect(bookingAPIs.getBookedSlots).toHaveBeenCalledWith({
      bookingPageId: 'booking-page-2',
      date: '2024-01-16',
    })

    const state = useBookingSlotsStore.getState()

    expect(state.selectedDate).toBe('2024-01-16')
    expect(state.bookedSlots).toHaveLength(1)
    expect(state.bookedSlots[0]?.status).toBe('pending')
  })

  it('should set selected date from Date object and load slots', async () => {
    const mockResponse = {
      status: { success: true, message: 'Success', code: '200' },
      data: {
        bookingPageId: 'booking-page-3',
        date: '2024-01-17',
        bookedSlots: [],
      },
    }

    vi.mocked(bookingAPIs.getBookedSlots).mockResolvedValue(mockResponse)

    const store = useBookingSlotsStore.getState()

    // Set booking page ID first
    await store.setBookingPageId('booking-page-3')

    // Set selected date from Date object using local timezone
    const testDate = new Date(2024, 0, 17) // January 17, 2024 in local timezone
    await store.setSelectedDateFromDateObject(testDate)

    expect(bookingAPIs.getBookedSlots).toHaveBeenCalledWith({
      bookingPageId: 'booking-page-3',
      date: '2024-01-17',
    })

    const state = useBookingSlotsStore.getState()

    expect(state.selectedDate).toBe('2024-01-17')
  })

  it('should format date correctly without timezone issues', async () => {
    const mockResponse = {
      status: { success: true, message: 'Success', code: '200' },
      data: {
        bookingPageId: 'booking-page-test',
        date: '2024-01-23',
        bookedSlots: [],
      },
    }

    vi.mocked(bookingAPIs.getBookedSlots).mockResolvedValue(mockResponse)

    const store = useBookingSlotsStore.getState()
    await store.setBookingPageId('booking-page-test')

    // Test various dates to ensure no timezone shifting
    const testCases = [
      { input: new Date(2024, 0, 23), expected: '2024-01-23' }, // January 23
      { input: new Date(2024, 11, 31), expected: '2024-12-31' }, // December 31
      { input: new Date(2024, 1, 29), expected: '2024-02-29' }, // February 29 (leap year)
    ]

    for (const testCase of testCases) {
      await store.setSelectedDateFromDateObject(testCase.input)
      const state = useBookingSlotsStore.getState()

      expect(state.selectedDate).toBe(testCase.expected)
    }
  })

  it('should check if slot is booked correctly', () => {
    const store = useBookingSlotsStore.getState()

    // Manually set some booked slots
    store.loadBookedSlots = vi.fn()
    useBookingSlotsStore.setState({
      bookedSlots: [
        {
          field: 'field-1',
          time: '10:00',
          date: '2024-01-15',
          bookingId: 'booking-1',
          status: 'confirmed',
        },
        {
          field: 'field-1',
          time: '11:00',
          date: '2024-01-15',
          bookingId: 'booking-2',
          status: 'cancelled',
        },
      ],
    })

    const state = useBookingSlotsStore.getState()

    // Should return true for confirmed booking
    expect(state.isSlotBooked('field-1', '10:00')).toBe(true)

    // Should return false for cancelled booking
    expect(state.isSlotBooked('field-1', '11:00')).toBe(false)

    // Should return false for non-existent booking
    expect(state.isSlotBooked('field-1', '12:00')).toBe(false)
  })

  it('should get slot status correctly', () => {
    // Manually set some booked slots
    useBookingSlotsStore.setState({
      bookedSlots: [
        {
          field: 'field-1',
          time: '10:00',
          date: '2024-01-15',
          bookingId: 'booking-1',
          status: 'confirmed',
        },
        {
          field: 'field-1',
          time: '11:00',
          date: '2024-01-15',
          bookingId: 'booking-2',
          status: 'pending',
        },
      ],
    })

    const state = useBookingSlotsStore.getState()

    expect(state.getSlotStatus('field-1', '10:00')).toBe('confirmed')
    expect(state.getSlotStatus('field-1', '11:00')).toBe('pending')
    expect(state.getSlotStatus('field-1', '12:00')).toBe('available')
  })

  it('should reload current slots when bookingPageId and selectedDate are available', async () => {
    // Mock the API call
    const mockGetBookedSlots = vi.fn().mockResolvedValue({
      status: { success: true },
      data: {
        bookedSlots: [
          {
            field: 'field-1',
            time: '10:00',
            date: '2024-01-15',
            bookingId: 'booking-1',
            status: 'confirmed',
          },
        ],
      },
    })

    // Mock the bookingAPIs module
    vi.doMock('@/modules/booking/booking.apis', () => ({
      bookingAPIs: {
        getBookedSlots: mockGetBookedSlots,
      },
    }))

    // Set initial state
    useBookingSlotsStore.setState({
      bookingPageId: 'page-1',
      selectedDate: '2024-01-15',
      bookedSlots: [],
    })

    const state = useBookingSlotsStore.getState()

    // Call reloadCurrentSlots
    await state.reloadCurrentSlots()

    // Verify API was called with correct parameters
    expect(mockGetBookedSlots).toHaveBeenCalledWith({
      bookingPageId: 'page-1',
      date: '2024-01-15',
    })
  })

  it('should not reload slots when bookingPageId or selectedDate is missing', async () => {
    // Mock the API call
    const mockGetBookedSlots = vi.fn()

    // Mock the bookingAPIs module
    vi.doMock('@/modules/booking/booking.apis', () => ({
      bookingAPIs: {
        getBookedSlots: mockGetBookedSlots,
      },
    }))

    // Set state without bookingPageId
    useBookingSlotsStore.setState({
      bookingPageId: null,
      selectedDate: '2024-01-15',
      bookedSlots: [],
    })

    const state = useBookingSlotsStore.getState()

    // Call reloadCurrentSlots
    await state.reloadCurrentSlots()

    // Verify API was not called
    expect(mockGetBookedSlots).not.toHaveBeenCalled()
  })
})
