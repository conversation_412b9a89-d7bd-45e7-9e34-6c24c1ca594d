import type { Booking, TimeSlot } from '../types'
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { generateTimeSlots } from '../utils'

// Define the store state interface
interface AvailabilityCalendarState {
  // Selected date state
  selectedDate: Date

  // Selected bookings state
  selectedBookings: Booking

  // Derived data (calculated from other state)
  timeSlots: TimeSlot[]

  // Actions
  setSelectedDate: (date: Date) => void
  setSelectedBookings: (bookings: Booking) => void
  addBooking: (fieldId: string, time: string, date: Date) => void
  removeBooking: (fieldId: string, time: string) => void
  clearBookings: () => void

  // Helpers
  isSlotSelected: (fieldId: string, time: string) => boolean
  toggleBookingSlot: (fieldId: string, time: string, date: Date) => void
}

// Create the store with performance optimizations
export const useAvailabilityCalendarStore = create<AvailabilityCalendarState>()(
  devtools(
    immer(
      (set, get) => ({
        // Initial state
        selectedDate: new Date(),
        selectedBookings: [],
        timeSlots: [],

        // Actions
        setSelectedDate: (date: Date) =>
          set((state) => {
            try {
              // Ensure currentDate is a valid Date object
              let currentDate: Date

              if (state.selectedDate instanceof Date) {
                // If it's already a Date object, use it directly
                currentDate = state.selectedDate
              } else if (state.selectedDate) {
                // If it's a string or timestamp, convert it to Date
                currentDate = new Date(state.selectedDate)
              } else {
                // If it's null/undefined, use current date
                currentDate = new Date()
              }

              // Ensure date is a valid Date object
              const newDate = date instanceof Date ? date : new Date(date)

              // Check if the date is different
              const isSameDate
                  = currentDate.getFullYear() === newDate.getFullYear()
                    && currentDate.getMonth() === newDate.getMonth()
                    && currentDate.getDate() === newDate.getDate()

              // Update the selected date
              state.selectedDate = newDate

              // Clear bookings if the date has changed
              if (!isSameDate) {
                state.selectedBookings = []
              }
            } catch (error) {
              // If any error occurs, just set the date and clear bookings
              console.error('Error in setSelectedDate:', error)
              state.selectedDate = date
              state.selectedBookings = []
            }
          }, false, 'setSelectedDate'),

        setSelectedBookings: (bookings: Booking) =>
          set((state) => {
            state.selectedBookings = bookings
          }, false, 'setSelectedBookings'),

        addBooking: (fieldId: string, time: string, date: Date) =>
          set((state) => {
            state.selectedBookings.push({
              date,
              field: fieldId,
              time,
            })
          }, false, 'addBooking'),

        removeBooking: (fieldId: string, time: string) =>
          set((state) => {
            const index = state.selectedBookings.findIndex(
              booking => booking.field === fieldId && booking.time === time,
            )
            if (index !== -1) {
              state.selectedBookings.splice(index, 1)
            }
          }, false, 'removeBooking'),

        clearBookings: () =>
          set((state) => {
            state.selectedBookings = []
          }, false, 'clearBookings'),

        // Helpers
        isSlotSelected: (fieldId: string, time: string) => {
          return get().selectedBookings.some(
            booking => booking.field === fieldId && booking.time === time,
          )
        },

        toggleBookingSlot: (fieldId: string, time: string, date: Date) => {
          const { isSlotSelected, removeBooking, addBooking } = get()

          if (isSlotSelected(fieldId, time)) {
            removeBooking(fieldId, time)
          } else {
            addBooking(fieldId, time, date)
          }
        },
      }),
    ),
  ),
)

// Create a separate store slice for configuration
interface AvailabilityCalendarConfig {
  // Configuration state
  businessHours: {
    start: string
    end: string
    daysOfWeek: number[]
  }
  timeSlotInterval: number
  disabledDates: string[]

  // Actions
  setBusinessHours: (businessHours: { start: string, end: string, daysOfWeek: number[] }) => void
  setTimeSlotInterval: (interval: number) => void
  setDisabledDates: (dates: string[]) => void

  // Computed values
  generateTimeSlotsForDate: (date: Date) => TimeSlot[]
}

// Create the configuration store
export const useAvailabilityCalendarConfig = create<AvailabilityCalendarConfig>()(
  devtools(
    immer(
      (set, get) => ({
        // Initial state with defaults
        businessHours: {
          start: '08:00',
          end: '22:00',
          daysOfWeek: [0, 1, 2, 3, 4, 5, 6], // All days of the week
        },
        timeSlotInterval: 60,
        disabledDates: [],

        // Actions
        setBusinessHours: businessHours =>
          set((state) => {
            state.businessHours = businessHours
          }, false, 'setBusinessHours'),

        setTimeSlotInterval: interval =>
          set((state) => {
            state.timeSlotInterval = interval
          }, false, 'setTimeSlotInterval'),

        setDisabledDates: dates =>
          set((state) => {
            state.disabledDates = dates
          }, false, 'setDisabledDates'),

        // Computed values
        generateTimeSlotsForDate: (date: Date) => {
          const { businessHours, timeSlotInterval, disabledDates } = get()
          return generateTimeSlots(date, businessHours, timeSlotInterval, disabledDates)
        },
      }),
    ),
  ),
)

// Selectors for performance optimization with proper typing
export const useSelectedDate = () => useAvailabilityCalendarStore<Date>(state => state.selectedDate)
export const useSelectedBookings = () => useAvailabilityCalendarStore<Booking>(state => state.selectedBookings)
export const useIsSlotSelected = () => useAvailabilityCalendarStore<(fieldId: string, time: string) => boolean>(state => state.isSlotSelected)
export const useToggleBookingSlot = () => useAvailabilityCalendarStore<(fieldId: string, time: string, date: Date) => void>(state => state.toggleBookingSlot)
export const useSetSelectedDate = () => useAvailabilityCalendarStore<(date: Date) => void>(state => state.setSelectedDate)
export const useClearBookings = () => useAvailabilityCalendarStore<() => void>(state => state.clearBookings)
export const useGenerateTimeSlotsForDate = () => useAvailabilityCalendarConfig<(date: Date) => TimeSlot[]>(state => state.generateTimeSlotsForDate)
