/* eslint-disable react-refresh/only-export-components */
import type { Booking, Field, TimeSlot } from './types'
import { useBookingSlotsStore } from '@/modules/booking/stores/booking-slots.store'
import React, { useEffect, useMemo } from 'react'
import {
  AvailabilityGrid,
  Legend,
} from './components'
import { DatePickerComponent } from './components/DatePickerComponent'
import {
  DEFAULT_BUSINESS_HOURS,
  DEFAULT_DISABLED_DATES,
  DEFAULT_FIELDS,
  DEFAULT_HIGHLIGHTED_DATES,
} from './constants'
import {
  useAvailabilityCalendarConfig,
  useAvailabilityCalendarStore,
  useSelectedBookings,
  useSelectedDate,
} from './store/availabilityCalendarStore'

export type AvailabilityCalendarBlockProps = {
  title?: string
  subtitle?: string
  bookingPageId?: string // ID of the booking page for loading booked slots
  minDate?: string // YYYY-MM-DD format
  maxDate?: string // YYYY-MM-DD format
  disabledDates?: string[] // Array of YYYY-MM-DD strings
  highlightedDates?: Array<{
    date: string
    color?: string
    tooltip?: string
  }>
  businessHours?: {
    start: string // HH:MM format
    end: string // HH:MM format
    daysOfWeek: number[] // 0 = Sunday, 1 = Monday, etc.
  }
  firstDayOfWeek?: 0 | 1 // 0 = Sunday, 1 = Monday
  timeSlotInterval?: number // in minutes
  showAvailabilityLegend?: boolean
  fields?: Field[]
}

/**
 * Availability Calendar Block Component
 * Displays a calendar with available time slots for booking
 */
export const AvailabilityCalendarBlockComponent: React.FC<AvailabilityCalendarBlockProps> = ({
  title = 'Đặt sân',
  subtitle,
  bookingPageId,
  minDate,
  maxDate,
  disabledDates = DEFAULT_DISABLED_DATES,
  highlightedDates = DEFAULT_HIGHLIGHTED_DATES,
  businessHours = DEFAULT_BUSINESS_HOURS,
  firstDayOfWeek = 1,
  timeSlotInterval = 60,
  showAvailabilityLegend = true,
  fields = DEFAULT_FIELDS,
}) => {
  // Get state and actions from stores with proper typing
  const selectedDate = useSelectedDate()
  const selectedBookings = useSelectedBookings()
  const setSelectedDate = useAvailabilityCalendarStore<(date: Date) => void>(state => state.setSelectedDate)
  const { isLoading: isLoadingSlots, error: slotsError, setBookingPageId } = useBookingSlotsStore()

  // Set booking page ID when component mounts or bookingPageId changes
  useEffect(() => {
    if (bookingPageId) {
      setBookingPageId(bookingPageId)
    }
  }, [bookingPageId, setBookingPageId])

  // Configure the store with props
  const setBusinessHours = useAvailabilityCalendarConfig<(businessHours: { start: string, end: string, daysOfWeek: number[] }) => void>(
    state => state.setBusinessHours,
  )
  const setTimeSlotInterval = useAvailabilityCalendarConfig<(interval: number) => void>(
    state => state.setTimeSlotInterval,
  )
  const setDisabledDates = useAvailabilityCalendarConfig<(dates: string[]) => void>(
    state => state.setDisabledDates,
  )
  const generateTimeSlotsForDate = useAvailabilityCalendarConfig<(date: Date) => TimeSlot[]>(
    state => state.generateTimeSlotsForDate,
  )

  // Initialize store with props on mount and when props change
  useEffect(() => {
    setBusinessHours(businessHours)
    setTimeSlotInterval(timeSlotInterval)
    setDisabledDates(disabledDates)
  }, [businessHours, timeSlotInterval, disabledDates, setBusinessHours, setTimeSlotInterval, setDisabledDates])

  // Parse min and max dates
  const parsedMinDate = minDate ? new Date(minDate) : undefined
  const parsedMaxDate = maxDate ? new Date(maxDate) : undefined

  // Parse disabled dates
  const parsedDisabledDates = useMemo(() => {
    return disabledDates.map(dateStr => new Date(dateStr))
  }, [disabledDates])

  // Generate time slots for the selected date
  const timeSlots = useMemo(() => {
    return generateTimeSlotsForDate(selectedDate)
  }, [selectedDate, generateTimeSlotsForDate])

  // Handle date selection
  // Note: Changing the date will automatically reset all selected bookings and load booked slots
  const handleDateSelect = async (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date) // This will also clear any selected bookings
      // Also update the booking slots store and load booked slots
      // await setSelectedDateFromDateObject(date)
    }
  }

  // Use fields from props or default sample fields if none provided
  const displayFields = useMemo(() => {
    return fields
  }, [fields])

  // Get the setSelectedBookings action from the store
  const setSelectedBookings = useAvailabilityCalendarStore<(bookings: Booking) => void>(state => state.setSelectedBookings)

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border">
      <div className="mb-4">
        <h2 className="text-lg font-semibold">{title}</h2>
        {subtitle && <p className="text-sm text-gray-500 mb-2">{subtitle}</p>}

        {/* Date selection */}
        <DatePickerComponent
          selectedDate={selectedDate}
          onDateSelect={handleDateSelect}
          parsedMinDate={parsedMinDate}
          parsedMaxDate={parsedMaxDate}
          parsedDisabledDates={parsedDisabledDates}
          highlightedDates={highlightedDates}
          firstDayOfWeek={firstDayOfWeek}
        />

        {/* Error message */}
        {slotsError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <p className="text-red-700 text-sm">
              Lỗi khi tải thông tin slot:
              {' '}
              {slotsError}
            </p>
          </div>
        )}

        {/* Loading indicator */}
        {isLoadingSlots && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <p className="text-blue-700 text-sm flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700"></div>
              Đang tải thông tin slot...
            </p>
          </div>
        )}

        {/* Availability grid */}
        <div className="mb-6">
          <AvailabilityGrid
            timeSlots={timeSlots}
            displayFields={displayFields}
            selectedDate={selectedDate}
            selectedBookings={selectedBookings}
            setSelectedBookings={setSelectedBookings}
          />
        </div>

        {/* Booking summary moved to BookingTicketBlock */}
      </div>

      {/* Legend */}
      <Legend show={showAvailabilityLegend} />
    </div>
  )
}

// Re-export components for use in other files
export * from './components'
export * from './store/availabilityCalendarStore'
export * from './types'
export * from './utils'
