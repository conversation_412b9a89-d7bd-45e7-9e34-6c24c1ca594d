import type { BookedSlot } from '@/modules/booking/booking.apis'
import { bookingAPIs } from '@/modules/booking/booking.apis'
import { formatDateYMD } from '@/utils/time'
import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'

// Helper function to format date to YYYY-MM-DD using local timezone
const formatDateToString = (date: Date | string): string => {
  if (typeof date === 'string') {
    return date
  }

  // Use local timezone instead of UTC to avoid date shifting
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

interface BookingSlotsState {
  // State
  bookedSlots: BookedSlot[]
  selectedDate: string | null
  bookingPageId: string | null
  isLoading: boolean
  error: string | null

  // Actions
  setBookingPageId: (id: string) => Promise<void>
  setSelectedDate: (date: string) => void
  setSelectedDateAndLoadSlots: (date: string) => Promise<void>
  setSelectedDateFromDateObject: (date: Date) => Promise<void>
  loadBookedSlots: (bookingPageId: string, date: string) => Promise<void>
  clearBookedSlots: () => void
  isSlotBooked: (field: string, time: string) => boolean
  getSlotStatus: (field: string, time: string) => 'available' | 'pending' | 'confirmed'
  // New action to reload current slots
  reloadCurrentSlots: () => Promise<void>
}

export const useBookingSlotsStore = create<BookingSlotsState>()(
  immer((set, get) => ({
    // Initial state
    bookedSlots: [],
    selectedDate: null,
    bookingPageId: null,
    isLoading: false,
    error: null,

    // Set booking page ID and load slots if date is already selected
    setBookingPageId: async (id: string) => {
      set((state) => {
        state.bookingPageId = id
      })
    },

    // Set selected date
    setSelectedDate: (date: string) => {
      set((state) => {
        state.selectedDate = date
      })
    },

    // Set selected date and automatically load booked slots
    setSelectedDateAndLoadSlots: async (date: string) => {
      const { bookingPageId, loadBookedSlots } = get()

      // Set the selected date first
      set((state) => {
        state.selectedDate = date
      })

      // If we have a booking page ID, load the booked slots for this date
      if (bookingPageId) {
        await loadBookedSlots(bookingPageId, date)
      }
    },

    // Set selected date from Date object and automatically load booked slots
    setSelectedDateFromDateObject: async (date: Date) => {
      const dateString = formatDateToString(date)
      const { bookingPageId, loadBookedSlots } = get()

      // Set the selected date first
      set((state) => {
        state.selectedDate = dateString
      })

      // If we have a booking page ID, load the booked slots for this date
      if (bookingPageId) {
        await loadBookedSlots(bookingPageId, dateString)
      }
    },

    // Load booked slots for a specific date
    loadBookedSlots: async (bookingPageId: string, date: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await bookingAPIs.getBookedSlots({
          bookingPageId,
          date,
        })

        if (response.status?.success) {
          set((state) => {
            state.bookedSlots = response.data?.bookedSlots || []
            state.selectedDate = date
            state.bookingPageId = bookingPageId
            state.isLoading = false
          })
        } else {
          throw new Error(response.status?.message || 'Failed to load booked slots')
        }
      } catch (error) {
        console.error('Error loading booked slots:', error)
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Unknown error'
          state.isLoading = false
          state.bookedSlots = []
        })
      }
    },

    // Clear booked slots
    clearBookedSlots: () => {
      set((state) => {
        state.bookedSlots = []
        state.selectedDate = null
        state.error = null
      })
    },

    // Check if a specific slot is booked
    isSlotBooked: (field: string, time: string) => {
      const { bookedSlots } = get()
      return bookedSlots.some(slot =>
        slot.field === field
        && formatDateYMD(slot.time) === formatDateYMD(time)
        && slot.status !== 'cancelled',
      )
    },

    // Get slot status
    getSlotStatus: (field: string, time: string) => {
      const { bookedSlots } = get()
      const slot = bookedSlots.find(slot =>
        slot.field === field && (slot.time) === (time),
      )

      if (!slot || slot.status === 'cancelled') {
        return 'available'
      }

      return slot.status
    },

    // Reload current slots - useful after booking submission
    reloadCurrentSlots: async () => {
      const { bookingPageId, selectedDate, loadBookedSlots } = get()

      if (bookingPageId && selectedDate) {
        await loadBookedSlots(bookingPageId, selectedDate)
      }
    },
  })),
)
